#!/usr/bin/env python3
"""
Test script for the new intent detection system with Gemma-27b and Flash 2.5 thinking.

This script demonstrates the new functionality:
1. Gemma-27b for intent detection with percentage confidence
2. Flash 2.5 with thinking budget for VPS tasks (50%-100% = 0-24576 thinking budget)
3. Direct chat responses for casual conversation

Usage:
    python test_new_intent_system.py

Note: This is for documentation purposes. The actual implementation is integrated
into the main VPS admin system.
"""

import asyncio
import os
import sys
from typing import Tuple

# Add the backend directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import Config
from ai_client import AIClient


async def test_intent_detection():
    """Test the new intent detection system."""
    print("=== Testing New Intent Detection System ===\n")
    
    # Initialize config and AI client
    config = Config()
    ai_client = AIClient(config)
    
    # Test messages
    test_messages = [
        "Hi there! How are you?",
        "What can you help me with?",
        "Check the status of nginx service",
        "Install docker on the server",
        "Deploy a complex web application with database",
        "List all running processes",
        "Thanks for your help!",
        "host this static web app: https://github.com/Moetez-Baklouti/portfolio"
    ]
    
    print("Testing intent detection with percentage confidence:\n")
    
    for message in test_messages:
        try:
            # Test the new intent detection
            intent_type, percentage, chat_response = await ai_client._detect_user_intent_with_percentage(
                message, ""
            )
            
            print(f"Message: '{message}'")
            print(f"Intent: {intent_type}")
            print(f"Percentage: {percentage}%")
            
            if chat_response:
                print(f"Chat Response: '{chat_response[:100]}...'")
            
            # Calculate thinking budget if VPS task
            if intent_type == "vps":
                thinking_budget = ai_client._calculate_thinking_budget(percentage)
                print(f"Thinking Budget: {thinking_budget}")
                
                if percentage >= config.MIN_THINKING_PERCENTAGE:
                    print("✓ Would use Flash 2.5 with thinking")
                else:
                    print("✓ Would use regular model (below thinking threshold)")
            else:
                print("✓ Would use direct chat response")
            
            print("-" * 60)
            
        except Exception as e:
            print(f"Error testing message '{message}': {e}")
            print("-" * 60)


async def test_thinking_budget_calculation():
    """Test thinking budget calculation."""
    print("\n=== Testing Thinking Budget Calculation ===\n")
    
    config = Config()
    ai_client = AIClient(config)
    
    test_percentages = [30, 40, 50, 60, 70, 80, 90, 100]
    
    print("Percentage -> Thinking Budget mapping:")
    print("(50%-100% maps to 0-24576 thinking budget)\n")
    
    for percentage in test_percentages:
        thinking_budget = ai_client._calculate_thinking_budget(percentage)
        use_thinking = percentage >= config.MIN_THINKING_PERCENTAGE
        
        print(f"{percentage:3d}% -> {thinking_budget:5d} thinking budget {'✓' if use_thinking else '✗'}")


def print_system_overview():
    """Print an overview of the new system."""
    print("\n=== New VPS Admin Intent System Overview ===\n")
    
    print("1. INTENT DETECTION (Gemma-27b):")
    print("   - Uses the prompt format from the image")
    print("   - Returns #Chat:(response) for casual conversation")
    print("   - Returns #VPS:(percentage%) for VPS tasks")
    print("   - Percentage indicates task difficulty/complexity")
    print()
    
    print("2. RESPONSE GENERATION:")
    print("   - Chat intents: Use direct response from Gemma-27b")
    print("   - VPS intents with ≥50%: Use Flash 2.5 with thinking")
    print("   - VPS intents with <50%: Use regular Gemini model")
    print()
    
    print("3. THINKING BUDGET CALCULATION:")
    print("   - 50%-100% difficulty maps to 0-24576 thinking budget")
    print("   - Higher percentage = more thinking budget")
    print("   - Only used when percentage ≥ MIN_THINKING_PERCENTAGE")
    print()
    
    print("4. CONFIGURATION (.env):")
    print("   - INTENT_MODEL: models/gemma-3-27b-it")
    print("   - FLASH_MODEL: gemini-2.5-flash-preview-05-20")
    print("   - USE_THINKING: true")
    print("   - MIN_THINKING_PERCENTAGE: 50")
    print()
    
    print("5. API CALL OPTIMIZATION:")
    print("   - Single intent detection call per message")
    print("   - Direct chat responses (no additional AI call)")
    print("   - Thinking only for complex VPS tasks")
    print("   - Caching for repeated messages")


async def main():
    """Main test function."""
    try:
        print_system_overview()
        await test_thinking_budget_calculation()
        await test_intent_detection()
        
        print("\n=== Test Complete ===")
        print("The new intent detection system is ready for use!")
        print("Start the VPS admin backend to test with real requests.")
        
    except Exception as e:
        print(f"Test failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
